const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
    const CardRequest = sequelize.define(
        "CardRequest",
        {
            card_request_id: {
                type: DataTypes.UUID,
                primaryKey: true,
                defaultValue: DataTypes.UUIDV4,
            },
            identity_id: {
                type: DataTypes.UUID,
                allowNull: false,
                references: {
                    model: "identity",
                    key: "identity_id",
                },
                onDelete: "CASCADE",
            },
            card_number: {
                type: DataTypes.STRING(50),
                allowNull: false,
                unique: true,
            },
            card_format: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            facility_code: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            pin: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            template: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            active_date: {
                type: DataTypes.DATE,
                allowNull: true,
            },
            deactive_date: {
                type: DataTypes.DATE,
                allowNull: true,
            },
            reason: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            shipping_required: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            status: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            pick_up_facility_id: {
                type: DataTypes.UUID,
                allowNull: true,
            },
            updated_by: {
                type: DataTypes.UUID,
                allowNull: true,
            },
        },
        {
            tableName: "card_request",
            timestamps: true,
            underscored: true,
        }
    );

    CardRequest.associate = (models) => {
        CardRequest.belongsTo(models.Identity, {
            foreignKey: "identity_id",
            as: "identity",
        });
        CardRequest.belongsTo(models.Facility, {
            foreignKey: "pick_up_facility_id",
            as: "pickup_facility_id",
            onDelete: "CASCADE",
        });
        CardRequest.hasOne(models.CardShipping, {
            foreignKey: "card_request_id",
            as: "card_shipping",
        });
        CardRequest.belongsTo(models.MasterData, {
            foreignKey: "template",
            targetKey: "key",
            as: "card_template_name",
            constraints: false,
            scope: {
                group: "card_template",
            },
        });
        CardRequest.belongsTo(models.MasterData, {
            foreignKey: "shipping_required",
            targetKey: "key",
            as: "shipping_required_name",
            constraints: false,
            scope: {
                group: "shipping_required",
            },
        });
        CardRequest.belongsTo(models.MasterData, {
            foreignKey: "status",
            targetKey: "key",
            as: "card_status_name",
            constraints: false,
            scope: {
                group: "card_status",
            },
        });
    };

    history(CardRequest, sequelize, DataTypes);

    return CardRequest;
};