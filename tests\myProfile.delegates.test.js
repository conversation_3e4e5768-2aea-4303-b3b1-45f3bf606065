const request = require('supertest');
const app = require('../app');
const { Delegates, Identity } = require('../models');

describe('MyProfile Delegates API', () => {
  let authToken;
  let testIdentityId;
  let otherIdentityId;

  beforeAll(async () => {
    // Setup test authentication token and identities
    // This would depend on your auth setup
    authToken = 'your-test-jwt-token';
    testIdentityId = 'test-identity-uuid';
    otherIdentityId = 'other-identity-uuid';
  });

  afterEach(async () => {
    // Clean up test data
    await Delegates.destroy({ where: {}, force: true });
  });

  describe('GET /api/my-profile/delegates', () => {
    it('should return delegates assigned to me and my delegates', async () => {
      // Create a delegation assigned to the test user
      await Delegates.create({
        name: '<PERSON>',
        eid: 'EMP001',
        task_to_delegate: 'Review expense reports',
        start_date: '2024-01-01',
        end_date: '2024-03-31',
        status: 1,
        identity_id: testIdentityId, // Assigned to test user
        created_by: otherIdentityId  // Created by someone else
      });

      // Create a delegation created by the test user
      await Delegates.create({
        name: '<PERSON>',
        eid: 'EMP002',
        task_to_delegate: 'Handle customer support',
        start_date: '2024-02-01',
        end_date: '2024-02-28',
        status: 1,
        identity_id: otherIdentityId, // Assigned to someone else
        created_by: testIdentityId    // Created by test user
      });

      const response = await request(app)
        .get('/api/my-profile/delegates')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('User delegates retrieved successfully');
      
      // Check delegates assigned to me
      expect(response.body.data.delegates_assigned_to_me).toBeDefined();
      expect(response.body.data.delegates_assigned_to_me.count).toBe(1);
      expect(response.body.data.delegates_assigned_to_me.data[0].name).toBe('John Doe');
      expect(response.body.data.delegates_assigned_to_me.data[0].task_to_delegate).toBe('Review expense reports');

      // Check my delegates
      expect(response.body.data.my_delegates).toBeDefined();
      expect(response.body.data.my_delegates.count).toBe(1);
      expect(response.body.data.my_delegates.data[0].name).toBe('Jane Smith');
      expect(response.body.data.my_delegates.data[0].task_to_delegate).toBe('Handle customer support');
    });

    it('should return empty arrays when no delegates exist', async () => {
      const response = await request(app)
        .get('/api/my-profile/delegates')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.delegates_assigned_to_me.count).toBe(0);
      expect(response.body.data.delegates_assigned_to_me.data).toEqual([]);
      expect(response.body.data.my_delegates.count).toBe(0);
      expect(response.body.data.my_delegates.data).toEqual([]);
    });

    it('should return only delegates assigned to me when I have no delegates', async () => {
      // Create only a delegation assigned to the test user
      await Delegates.create({
        name: 'Test Delegate',
        eid: 'EMP003',
        task_to_delegate: 'Test task',
        start_date: '2024-01-01',
        status: 1,
        identity_id: testIdentityId,
        created_by: otherIdentityId
      });

      const response = await request(app)
        .get('/api/my-profile/delegates')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.delegates_assigned_to_me.count).toBe(1);
      expect(response.body.data.my_delegates.count).toBe(0);
    });

    it('should return only my delegates when no delegates are assigned to me', async () => {
      // Create only a delegation created by the test user
      await Delegates.create({
        name: 'My Delegate',
        eid: 'EMP004',
        task_to_delegate: 'My task',
        start_date: '2024-01-01',
        status: 1,
        identity_id: otherIdentityId,
        created_by: testIdentityId
      });

      const response = await request(app)
        .get('/api/my-profile/delegates')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.delegates_assigned_to_me.count).toBe(0);
      expect(response.body.data.my_delegates.count).toBe(1);
    });

    it('should fail without authentication', async () => {
      await request(app)
        .get('/api/my-profile/delegates')
        .expect(401);
    });

    it('should handle multiple delegates correctly', async () => {
      // Create multiple delegations
      await Delegates.create({
        name: 'Delegate 1',
        eid: 'EMP001',
        task_to_delegate: 'Task 1',
        start_date: '2024-01-01',
        status: 1,
        identity_id: testIdentityId,
        created_by: otherIdentityId
      });

      await Delegates.create({
        name: 'Delegate 2',
        eid: 'EMP002',
        task_to_delegate: 'Task 2',
        start_date: '2024-01-02',
        status: 1,
        identity_id: testIdentityId,
        created_by: otherIdentityId
      });

      await Delegates.create({
        name: 'My Delegate 1',
        eid: 'EMP003',
        task_to_delegate: 'My Task 1',
        start_date: '2024-01-03',
        status: 1,
        identity_id: otherIdentityId,
        created_by: testIdentityId
      });

      const response = await request(app)
        .get('/api/my-profile/delegates')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.delegates_assigned_to_me.count).toBe(2);
      expect(response.body.data.my_delegates.count).toBe(1);
    });
  });
});
