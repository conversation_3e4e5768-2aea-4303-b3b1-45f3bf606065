const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const { MyProfileController } = require('../controllers');
const { MyProfileValidation } = require('../validations');
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: MyProfile
 *   description: Logged User profile management
 */
/**
 * @swagger
 * /my-profile:
 *   get:
 *     summary: Get current user profile details
 *     description: Retrieves the profile information of the currently authenticated user based on JWT token. No payload required.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User profile retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     identity_id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     email:
 *                       type: string
 *                       format: email
 *                       example: "<EMAIL>"
 *                     first_name:
 *                       type: string
 *                       example: "John"
 *                     last_name:
 *                       type: string
 *                       example: "Doe"
 *                     middle_name:
 *                       type: string
 *                       example: "Michael"
 *                     eid:
 *                       type: string
 *                       example: "EMP001"
 *                     identity_type:
 *                       type: integer
 *                       example: 1
 *                     identity_type_name:
 *                       type: string
 *                       example: "Employee"
 *                     national_id:
 *                       type: string
 *                       example: "123456789"
 *                     mobile:
 *                       type: string
 *                       example: "+1234567890"
 *                     start_date:
 *                       type: string
 *                       format: date
 *                       example: "2023-01-01"
 *                     end_date:
 *                       type: string
 *                       format: date
 *                       example: "2024-12-31"
 *                     status:
 *                       type: integer
 *                       example: 1
 *                     status_name:
 *                       type: string
 *                       example: "Active"
 *                     suspension:
 *                       type: boolean
 *                       example: false
 *                     suspension_date:
 *                       type: string
 *                       format: date
 *                       example: null
 *                     reason:
 *                       type: string
 *                       example: null
 *                     image:
 *                       type: string
 *                       example: "profile_image_url.jpg"
 *                     company:
 *                       type: string
 *                       example: "CareMate Inc."
 *                     organization:
 *                       type: string
 *                       example: "IT Department"
 *                     company_code:
 *                       type: string
 *                       example: "CM001"
 *                     job_title:
 *                       type: string
 *                       example: "Software Engineer"
 *                     job_code:
 *                       type: string
 *                       example: "SE001"
 *                     manager:
 *                       type: string
 *                       format: uuid
 *                       example: "456e7890-e89b-12d3-a456-************"
 *                     facility:
 *                       type: object
 *                       properties:
 *                         facility_id:
 *                           type: string
 *                           format: uuid
 *                         name:
 *                           type: string
 *                         address:
 *                           type: object
 *                           properties:
 *                             address_line_1:
 *                               type: string
 *                             address_line_2:
 *                               type: string
 *                             postal_code:
 *                               type: string
 *                             region:
 *                               type: string
 *                             country:
 *                               type: string
 *                             state:
 *                               type: string
 *                     language_preference:
 *                       type: object
 *                       properties:
 *                         language_id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         code:
 *                           type: string
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: User profile not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User profile not found"
 */
router.get(
  "/",
  auth(),
  MyProfileController.getCurrentUserProfile
);

/**
 * @swagger
 * /my-profile/cards:
 *   get:
 *     summary: Get current user's cards
 *     description: Retrieves all cards associated with the currently authenticated user based on JWT token. No payload required.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: User cards retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User cards retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       card_id:
 *                         type: string
 *                         format: uuid
 *                         example: "123e4567-e89b-12d3-a456-************"
 *                         description: Unique identifier for the card
 *                       card_number:
 *                         type: string
 *                         example: "1234567890"
 *                         description: The card number
 *                       card_format:
 *                         type: integer
 *                         example: 1
 *                         description: Format type of the card
 *                       card_format_name:
 *                         type: string
 *                         example: "Proximity Card"
 *                         description: Human readable name for card format
 *                       facility_code:
 *                         type: integer
 *                         example: 123
 *                         description: Facility code associated with the card
 *                       pin:
 *                         type: integer
 *                         example: 1234
 *                         description: PIN number for the card
 *                       template:
 *                         type: integer
 *                         example: 1
 *                         description: Template type for the card
 *                       card_template_name:
 *                         type: string
 *                         example: "Standard Template"
 *                         description: Human readable name for card template
 *                       active_date:
 *                         type: string
 *                         format: date
 *                         example: "2023-01-01"
 *                         description: Date when the card becomes active
 *                       deactive_date:
 *                         type: string
 *                         format: date
 *                         example: "2024-12-31"
 *                         description: Date when the card becomes inactive
 *                       reason:
 *                         type: string
 *                         example: "Employee card"
 *                         description: Reason for card issuance
 *                       status:
 *                         type: integer
 *                         example: 1
 *                         description: Current status of the card
 *                       card_status_name:
 *                         type: string
 *                         example: "Active"
 *                         description: Human readable name for card status
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Card creation timestamp
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Card last update timestamp
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: No cards found for the user
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No cards found for the user"
 */
router.get(
    "/cards",
    auth(),
    MyProfileController.getCurrentUserCard
)

/**
 * @swagger
 * /my-profile/access:
 *   get:
 *     summary: Get current user's access records
 *     description: Retrieves all access records for the currently authenticated user including area names, start dates, end dates, and status. No payload required.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: User access records retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User access records retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       identity_access_id:
 *                         type: string
 *                         format: uuid
 *                         example: "123e4567-e89b-12d3-a456-************"
 *                         description: Unique identifier for the access record
 *                       area_name:
 *                         type: string
 *                         example: "Main Building Lobby"
 *                         description: Name of the access area (from pacs_area_name or access level name)
 *                       access_level_name:
 *                         type: string
 *                         example: "Employee Access Level"
 *                         description: Name of the access level
 *                       description:
 *                         type: string
 *                         example: "Standard employee access to main building areas"
 *                         description: Description of the access level
 *                       access_level_type:
 *                         type: integer
 *                         example: 1
 *                         description: Type of access level
 *                       access_level_type_name:
 *                         type: string
 *                         example: "Employee"
 *                         description: Human readable name for access level type
 *                       start_date:
 *                         type: string
 *                         format: date
 *                         example: "2023-01-01"
 *                         description: Date when access becomes active
 *                       end_date:
 *                         type: string
 *                         format: date
 *                         example: "2024-12-31"
 *                         description: Date when access expires
 *                       status:
 *                         type: integer
 *                         example: 1
 *                         description: Current status of the access record
 *                       status_name:
 *                         type: string
 *                         example: "Active"
 *                         description: Human readable name for access status
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Access record creation timestamp
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Access record last update timestamp
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: No access records found for the user
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No access records found for the user"
 */
router.get(
    "/access",
    auth(),
    MyProfileController.getCurrentUserAccess
)

/**
 * @swagger
 * /my-profile/vehicles:
 *   get:
 *     summary: Get current user's vehicles
 *     description: Retrieves all vehicles registered to the currently authenticated user based on JWT token. No payload required.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: User vehicles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User vehicles retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       vehicle_id:
 *                         type: string
 *                         format: uuid
 *                         example: "123e4567-e89b-12d3-a456-************"
 *                         description: Unique identifier for the vehicle
 *                       plate_number:
 *                         type: string
 *                         example: "ABC-1234"
 *                         description: Vehicle license plate number
 *                       issued_by:
 *                         type: string
 *                         example: "Department of Motor Vehicles"
 *                         description: Authority that issued the plate
 *                       vin:
 *                         type: string
 *                         example: "1HGBH41JXMN109186"
 *                         description: Vehicle Identification Number (17 characters)
 *                       year:
 *                         type: integer
 *                         example: 2020
 *                         description: Manufacturing year of the vehicle
 *                       make:
 *                         type: string
 *                         example: "Toyota"
 *                         description: Vehicle manufacturer
 *                       model:
 *                         type: string
 *                         example: "Camry"
 *                         description: Vehicle model
 *                       color:
 *                         type: string
 *                         example: "Blue"
 *                         description: Vehicle color
 *                       uploaded_date:
 *                         type: string
 *                         format: date
 *                         example: "2023-01-15"
 *                         description: Date when vehicle information was uploaded
 *                       created_by:
 *                         type: string
 *                         format: uuid
 *                         example: "456e7890-e89b-12d3-a456-************"
 *                         description: ID of user who created the vehicle record
 *                       updated_by:
 *                         type: string
 *                         format: uuid
 *                         example: "456e7890-e89b-12d3-a456-************"
 *                         description: ID of user who last updated the vehicle record
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Vehicle record creation timestamp
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Vehicle record last update timestamp
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: No vehicles found for the user
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No vehicles found for the user"
 */
router.get(
    "/vehicles",
    auth(),
    MyProfileController.getCurrentUserVehicles
)

/**
 * @swagger
 * /my-profile/corporate:
 *   get:
 *     summary: Get corporate information for the current user
 *     description: Retrieves corporate information including hiring organization, corporate identity, facility information, and address details for the currently authenticated user.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: Corporate information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Corporate information retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     hiring_organization:
 *                       type: object
 *                       properties:
 *                         type:
 *                           type: string
 *                           example: "Employee"
 *                           description: Type of identity (Employee, Contractor, etc.)
 *                         status:
 *                           type: string
 *                           example: "ACTIVE"
 *                           description: Current status of the user
 *                         hiring_company:
 *                           type: string
 *                           example: "ORACLE AMERICA, INC."
 *                           description: Company that hired the user
 *                         hiring_organization:
 *                           type: string
 *                           example: "ORCL US"
 *                           description: Organization within the company
 *                         start_date:
 *                           type: string
 *                           format: date
 *                           example: "2021-01-11"
 *                           description: Start date of employment
 *                         legacy_cost_center:
 *                           type: string
 *                           example: "6EP2 - OCI Physical Security"
 *                           description: Legacy cost center information
 *                     corporate_identity:
 *                       type: object
 *                       properties:
 *                         hcmid:
 *                           type: string
 *                           example: "300016491970"
 *                           description: Human Capital Management ID
 *                         user_name:
 *                           type: string
 *                           example: "<EMAIL>"
 *                           description: User's email/username
 *                         oracle_id:
 *                           type: string
 *                           example: "*********"
 *                           description: Oracle ID or company code
 *                         manager_sponsor:
 *                           type: string
 *                           example: "Steven Kruschke *********"
 *                           description: Manager or sponsor information
 *                     facility_information:
 *                       type: object
 *                       properties:
 *                         facility:
 *                           type: string
 *                           example: "Santa Clara"
 *                           description: Facility name where user is located
 *                     address:
 *                       type: object
 *                       properties:
 *                         work_address_line1:
 *                           type: string
 *                           example: "4030 George Sellon Circle"
 *                           description: Primary work address line
 *                         work_address_line2:
 *                           type: string
 *                           example: ""
 *                           description: Secondary work address line
 *                         country:
 *                           type: string
 *                           example: "United States"
 *                           description: Country of work location
 *                         state:
 *                           type: string
 *                           example: "CA"
 *                           description: State or province of work location
 *                         city:
 *                           type: string
 *                           example: "Santa Clara"
 *                           description: City of work location
 *                         postal_code:
 *                           type: string
 *                           example: "95054"
 *                           description: Postal code of work location
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User not found"
 */
router.get(
    "/corporate",
    auth(),
    MyProfileController.getCurrentUserCorporate
)

/**
 * @swagger
 * /my-profile/card-request:
 *   post:
 *     summary: Create a card request for current user
 *     tags: [MyProfile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               card_number:
 *                 type: string
 *               card_format:
 *                 type: integer
 *               facility_code:
 *                 type: integer
 *               pin:
 *                 type: integer
 *               template:
 *                 type: integer
 *               active_date:
 *                 type: string
 *                 format: date
 *               deactive_date:
 *                 type: string
 *                 format: date
 *               reason:
 *                 type: string
 *               shipping_required:
 *                 type: integer
 *               status:
 *                 type: integer
 *               pick_up_facility_id:
 *                 type: string
 *     responses:
 *       201:
 *         description: Card request created successfully
 *       400:
 *         description: Invalid input
 */
router.post(
    "/card-request",
    auth(),
    validate(MyProfileValidation.createCardRequest),
    MyProfileController.createCardRequest
);

module.exports = router;