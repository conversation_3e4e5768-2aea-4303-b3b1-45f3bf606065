const { Identity, Facility, Address, Country, State, MasterData, Language, LanguagePreference, Card, CardRequest, CardShipping, sequelize } = require('../models');
const { sendSuccess, sendError, catchAsync } = require('../helpers/api.helper');
const { status: httpStatus } = require('http-status');

/**
 * @desc    Get current user profile details
 * @param   {Object} req - Express request object
 * @param   {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object with user profile information
 */
exports.getCurrentUserProfile = catchAsync(async (req, res) => {
    const { identity_id } = req.identity;

    if (!identity_id) {
        return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
    }

    // Fetch user profile with related data
    const userProfile = await Identity.findByPk(identity_id, {
        attributes: [
            'identity_id',
            'email',
            'first_name',
            'last_name',
            'middle_name',
            'eid',
            'identity_type',
            'national_id',
            'mobile',
            'start_date',
            'end_date',
            'status',
            'suspension',
            'suspension_date',
            'reason',
            'image',
            'company',
            'organization',
            'company_code',
            'job_title',
            'job_code',
            'manager',
            'created_at',
            'updated_at'
        ],
        include: [
            {
                model: Facility,
                as: 'facility',
                attributes: ['facility_id', 'name'],
                include: [
                    {
                        model: Address,
                        as: 'address',
                        attributes: [
                            'address_line_1',
                            'address_line_2',
                            'postal_code',
                            'region'
                        ],
                        include: [
                            {
                                model: Country,
                                as: 'country',
                                attributes: ['name']
                            },
                            {
                                model: State,
                                as: 'state',
                                attributes: ['name']
                            }
                        ]
                    }
                ]
            },
            {
                model: MasterData,
                as: 'identity_status_name',
                attributes: ['value']
            },
            {
                model: MasterData,
                as: 'identity_type_name',
                attributes: ['value']
            }
        ]
    });

    if (!userProfile) {
        return sendError(res, "User profile not found", httpStatus.NOT_FOUND);
    }

    // Get language preference
    const languagePreference = await LanguagePreference.findOne({
        where: { identity_id },
        include: [
            {
                model: Language,
                as: 'language',
                attributes: ['language_id', 'name', 'code']
            }
        ]
    });

    // Format response data
    const profileData = {
        identity_id: userProfile.identity_id,
        email: userProfile.email,
        first_name: userProfile.first_name,
        last_name: userProfile.last_name,
        middle_name: userProfile.middle_name,
        eid: userProfile.eid,
        identity_type: userProfile.identity_type,
        identity_type_name: userProfile.identity_type_name?.value || null,
        national_id: userProfile.national_id,
        mobile: userProfile.mobile,
        start_date: userProfile.start_date,
        end_date: userProfile.end_date,
        status: userProfile.status,
        status_name: userProfile.identity_status_name?.value || null,
        suspension: userProfile.suspension,
        suspension_date: userProfile.suspension_date,
        reason: userProfile.reason,
        image: userProfile.image,
        company: userProfile.company,
        organization: userProfile.organization,
        company_code: userProfile.company_code,
        job_title: userProfile.job_title,
        job_code: userProfile.job_code,
        manager: userProfile.manager,
        facility: userProfile.facility ? {
            facility_id: userProfile.facility.facility_id,
            name: userProfile.facility.name,
            address: userProfile.facility.address ? {
                address_line_1: userProfile.facility.address.address_line_1,
                address_line_2: userProfile.facility.address.address_line_2,
                postal_code: userProfile.facility.address.postal_code,
                region: userProfile.facility.address.region,
                country: userProfile.facility.address.country?.name || null,
                state: userProfile.facility.address.state?.name || null
            } : null
        } : null,
        language_preference: languagePreference ? {
            language_id: languagePreference.language?.language_id || null,
            name: languagePreference.language?.name || null,
            code: languagePreference.language?.code || null
        } : null,
        created_at: userProfile.created_at,
        updated_at: userProfile.updated_at
    };

    sendSuccess(res, "User profile retrieved successfully", httpStatus.OK, profileData);
});

/**
 * @desc    Get current user card details
 * @param   {Object} req - Express request object
 * @param   {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object with user card information
 */
exports.getCurrentUserCard = catchAsync(async (req, res) => {
    const { identity_id } = req.identity;

    if (!identity_id) {
        return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
    }

    const cards = await Card.findAll({
        where: { identity_id },
        attributes: [
            'card_id',
            'card_number',
            'card_format',
            'facility_code',
            'pin',
            'template',
            'active_date',
            'deactive_date',
            'reason',
            'status',
            'created_at',
            'updated_at'
        ],
        include: [
            {
                model: MasterData,
                as: 'card_format_name',
                attributes: ['value']
            },
            {
                model: MasterData,
                as: 'card_template_name',
                attributes: ['value']
            },
            {
                model: MasterData,
                as: 'card_status_name',
                attributes: ['value']
            }
        ]
    });

    if (!cards || cards.length === 0) {
        return sendError(res, "No cards found for the user", httpStatus.NOT_FOUND);
    }

    // Format the response data
    const formattedCards = cards.map(card => ({
        card_id: card.card_id,
        card_number: card.card_number,
        card_format: card.card_format,
        card_format_name: card.card_format_name?.value || null,
        facility_code: card.facility_code,
        pin: card.pin,
        template: card.template,
        card_template_name: card.card_template_name?.value || null,
        active_date: card.active_date,
        deactive_date: card.deactive_date,
        reason: card.reason,
        status: card.status,
        card_status_name: card.card_status_name?.value || null,
        created_at: card.created_at,
        updated_at: card.updated_at
    }));

    sendSuccess(res, "User cards retrieved successfully", httpStatus.OK, formattedCards);
})

/**
 * @desc Create a card request for current user with optional shipping details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing the created card request
 */
exports.createCardRequest = catchAsync(async (req, res) => {
  // Get identity_id from authenticated user token
  const identity_id = req.identity.identity_id || req.identity.id;
  
  // Separate card request data from shipping data
  const {
    ship_to,
    ship_to_name,
    address_line_1,
    address_line_2,
    country_id,
    state_id,
    zip_code,
    mobile_phone,
    ...cardRequestData
  } = req.body;

  // Clean up date fields - convert empty strings to null
  const dateFields = ['active_date', 'deactive_date'];
  dateFields.forEach(field => {
    if (cardRequestData[field] === '') {
      cardRequestData[field] = null;
    }
  });

  // Add identity_id and updated_by from token
  cardRequestData.identity_id = identity_id;
  cardRequestData.updated_by = identity_id;

  // Use transaction to ensure both records are created together
  const result = await sequelize.transaction(async (t) => {
    // Create card request
    const cardRequest = await CardRequest.create(cardRequestData, { transaction: t });

    // Create shipping details if shipping is required
    if (cardRequestData.shipping_required === 1) {
      const shippingData = {
        card_request_id: cardRequest.card_request_id,
        ship_to,
        ship_to_name,
        address_line_1,
        address_line_2,
        country_id,
        state_id,
        zip_code,
        mobile_phone,
        updated_by: identity_id,
      };

      const cardShipping = await CardShipping.create(shippingData, { transaction: t });
      
      return {
        card_request: cardRequest,
        shipping_details: cardShipping
      };
    }

    return { card_request: cardRequest };
  });

  sendSuccess(res, "Card request created successfully", httpStatus.CREATED, result);
})



/**
 * @desc    Get current user access details
 * @param   {Object} req - Express request object
 * @param   {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object with user access information including area name, start date, end date, and status
 */
exports.getCurrentUserAccess = catchAsync(async (req, res) => {
    const { identity_id } = req.identity;

    if (!identity_id) {
        return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
    }

    // Import models locally
    const { IdentityAccess, AccessLevel } = require('../models');

    const accessRecords = await IdentityAccess.findAll({
        where: { identity_id },
        attributes: [
            'identity_access_id',
            'start_date',
            'end_date',
            'status',
            'created_at',
            'updated_at'
        ],
        include: [
            {
                model: AccessLevel,
                as: 'access_level',
                attributes: [
                    'access_level_id',
                    'name',
                    'pacs_area_name',
                    'description',
                    'access_level_type'
                ],
                include: [
                    {
                        model: MasterData,
                        as: 'access_level_type_name',
                        attributes: ['value']
                    }
                ]
            },
            {
                model: MasterData,
                as: 'identity_access_status_name',
                attributes: ['value']
            }
        ],
        order: [['created_at', 'DESC']]
    });

    if (!accessRecords || accessRecords.length === 0) {
        return sendError(res, "No access records found for the user", httpStatus.NOT_FOUND);
    }

    // Format the response data
    const formattedAccess = accessRecords.map(access => ({
        identity_access_id: access.identity_access_id,
        area_name: access.access_level?.pacs_area_name || access.access_level?.name || null,
        access_level_name: access.access_level?.name || null,
        description: access.access_level?.description || null,
        access_level_type: access.access_level?.access_level_type || null,
        access_level_type_name: access.access_level?.access_level_type_name?.value || null,
        start_date: access.start_date,
        end_date: access.end_date,
        status: access.status,
        status_name: access.identity_access_status_name?.value || null,
        created_at: access.created_at,
        updated_at: access.updated_at
    }));

    sendSuccess(res, "User access records retrieved successfully", httpStatus.OK, formattedAccess);
})



exports.getCurrentUserVehicles = catchAsync(async (req, res) => {
    const { identity_id } = req.identity;
    if (!identity_id) {
        return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
    }
    const { Vehicle } = require('../models');
    const vehicles = await Vehicle.findAll({
        where: { identity_id },
        attributes: ['vehicle_id', 'plate_number', 'issued_by', 'vin', 'year', 'make', 'model', 'color', 'uploaded_date', 'created_by', 'updated_by', 'created_at', 'updated_at'],
        order: [['created_at', 'DESC']]
    });
    if (!vehicles || vehicles.length === 0) {
        return sendError(res, "No vehicles found for the user", httpStatus.NOT_FOUND);
    }
    const formattedVehicles = vehicles.map(vehicle => ({
        vehicle_id: vehicle.vehicle_id,
        plate_number: vehicle.plate_number,
        issued_by: vehicle.issued_by,
        vin: vehicle.vin,
        year: vehicle.year,
        make: vehicle.make,
        model: vehicle.model,
        color: vehicle.color,
        uploaded_date: vehicle.uploaded_date,
        created_by: vehicle.created_by,
        updated_by: vehicle.updated_by,
        created_at: vehicle.created_at,
        updated_at: vehicle.updated_at
    }));
    sendSuccess(res, "User vehicles retrieved successfully", httpStatus.OK, formattedVehicles);
})

/**
 * @desc    Get corporate information for the current user
 * @param   {Object} req - Express request object
 * @param   {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object with corporate information
 */
exports.getCurrentUserCorporate = catchAsync(async (req, res) => {
    const { identity_id } = req.identity;

    if (!identity_id) {
        return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
    }

    const identity = await Identity.findByPk(identity_id, {
        attributes: [
            'identity_id',
            'eid',
            'first_name',
            'last_name',
            'email',
            'company',
            'organization',
            'company_code',
            'job_title',
            'job_code',
            'manager',
            'start_date',
            'status'
        ],
        include: [
            {
                model: Facility,
                as: 'facility',
                attributes: ['facility_id', 'name', 'facility_code'],
                include: [
                    {
                        model: Address,
                        as: 'address',
                        attributes: [
                            'address_line_1',
                            'address_line_2',
                            'postal_code',
                            'region'
                        ],
                        include: [
                            {
                                model: Country,
                                as: 'country',
                                attributes: ['name']
                            },
                            {
                                model: State,
                                as: 'state',
                                attributes: ['name']
                            }
                        ]
                    }
                ]
            },
            {
                model: MasterData,
                as: 'identity_status_name',
                attributes: ['value']
            }
        ]
    });

    if (!identity) {
        return sendError(res, "User not found", httpStatus.NOT_FOUND);
    }

    // Format the response to match the structure shown in the image
    const corporateInfo = {
        hiring_organization: {
            type: "Employee", // This could be dynamic based on identity_type
            status: identity.identity_status_name?.value || "UNKNOWN",
            hiring_company: identity.company || "",
            hiring_organization: identity.organization || "",
            start_date: identity.start_date ? identity.start_date.toISOString().split('T')[0] : null,
            legacy_cost_center: "" // This field might need to be added to the model if required
        },
        corporate_identity: {
            hcmid: identity.eid || "",
            user_name: identity.email || "",
            oracle_id: identity.company_code || "",
            manager_sponsor: identity.manager || ""
        },
        facility_information: {
            facility: identity.facility?.name || ""
        },
        address: identity.facility?.address ? {
            work_address_line1: identity.facility.address.address_line_1 || "",
            work_address_line2: identity.facility.address.address_line_2 || "",
            country: identity.facility.address.country?.name || "",
            state: identity.facility.address.state?.name || "",
            city: identity.facility.address.region || "",
            postal_code: identity.facility.address.postal_code || ""
        } : {
            work_address_line1: "",
            work_address_line2: "",
            country: "",
            state: "",
            city: "",
            postal_code: ""
        }
    };

    sendSuccess(res, "Corporate information retrieved successfully", httpStatus.OK, corporateInfo);
})
